<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>💖 Valentine's Fireworks Spectacular 💖</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600&family=Playfair+Display:wght@400;700;900&family=Dancing+Script:wght@400;700&display=swap" rel="stylesheet">

<style>
:root {
    --bg-1: #0a0a0b;
    --bg-2: #1a1a2e;
    --bg-3: #16213e;
    --primary: #ff1744;
    --primary-light: #ff5983;
    --primary-dark: #c51162;
    --secondary: #e91e63;
    --accent: #ff6b9d;
    --green: #2e7d32;
    --green-light: #4caf50;
    --text: #ffffff;
    --text-soft: #e8eaf6;
    --gold: #ffd700;
    --pink: #ff69b4;
}

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text);
    font-family: 'Inter', sans-serif;
    overflow: hidden;
    position: relative;
}

/* Background slideshow */
.background-slideshow {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.background-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 2s ease-in-out;
}

.background-slide.active {
    opacity: 0.3;
}

/* Overlay to ensure content visibility */
.background-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 23, 68, 0.25) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 157, 0.25) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(26, 26, 46, 0.85) 0%, transparent 50%),
        linear-gradient(135deg, var(--bg-1) 0%, var(--bg-2) 50%, var(--bg-3) 100%);
    z-index: -1;
}

/* Animated background particles */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 107, 157, 0.4), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 23, 68, 0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.2), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: sparkle 20s linear infinite;
    pointer-events: none;
}

@keyframes sparkle {
    0% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
}

.scene {
    perspective: 1200px;
    text-align: center;
    position: relative;
    z-index: 10;
}

.click-me {
    margin-bottom: 30px;
    opacity: 0.9;
    animation: pulse 2s infinite, float 3s ease-in-out infinite;
    font-size: 1.2rem;
    font-weight: 400;
    letter-spacing: 1px;
    text-shadow: 0 0 30px rgba(255, 107, 157, 0.8);
    background: linear-gradient(45deg, var(--primary-light), var(--accent), var(--gold));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* FIREWORKS LAUNCHER */
.fireworks-launcher {
    position: relative;
    width: 200px;
    height: 100px;
    border: none;
    background: linear-gradient(135deg,
        var(--bg-2) 0%,
        var(--bg-3) 50%,
        var(--primary-dark) 100%);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.5),
        inset 0 2px 10px rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.fireworks-launcher:hover {
    transform: scale(1.05);
    box-shadow:
        0 15px 40px rgba(255, 23, 68, 0.3),
        inset 0 2px 10px rgba(255, 255, 255, 0.2);
}

.fireworks-launcher:active {
    transform: scale(0.98);
}

.launcher-text {
    color: var(--text);
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    line-height: 100px;
    text-shadow: 0 0 20px rgba(255, 107, 157, 0.5);
}

/* FIREWORKS CONTAINER */
.fireworks-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

/* FIREWORK PARTICLE */
.firework {
    position: absolute;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    pointer-events: none;
}

.firework-trail {
    position: absolute;
    width: 2px;
    height: 20px;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.8) 0%,
        var(--primary) 50%,
        transparent 100%);
    border-radius: 2px;
    transform-origin: bottom center;
}

/* FIREWORK EXPLOSION */
.explosion {
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.explosion-particle {
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* ROCKET TRAIL */
.rocket {
    position: absolute;
    width: 3px;
    height: 15px;
    background: linear-gradient(180deg,
        var(--gold) 0%,
        var(--primary) 50%,
        transparent 100%);
    border-radius: 3px;
    box-shadow: 0 0 10px var(--primary);
}

/* FIREWORK ANIMATIONS */
@keyframes rocketLaunch {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(-400px) scale(0.5);
        opacity: 0;
    }
}

@keyframes explode {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    20% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

@keyframes particleSpread {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) translate(var(--dx), var(--dy)) scale(0);
        opacity: 0;
    }
}

@keyframes sparkleTrail {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
    100% {
        transform: translateY(20px) scale(0);
        opacity: 0;
    }
}

@keyframes heartBurst {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(0) rotate(360deg);
        opacity: 0;
    }
}

/* FIREWORK COLORS */
.firework-red { background: radial-gradient(circle, #ff1744, #c51162); }
.firework-pink { background: radial-gradient(circle, #e91e63, #ad1457); }
.firework-purple { background: radial-gradient(circle, #9c27b0, #6a1b9a); }
.firework-blue { background: radial-gradient(circle, #2196f3, #1565c0); }
.firework-cyan { background: radial-gradient(circle, #00bcd4, #00838f); }
.firework-green { background: radial-gradient(circle, #4caf50, #2e7d32); }
.firework-yellow { background: radial-gradient(circle, #ffeb3b, #f57f17); }
.firework-orange { background: radial-gradient(circle, #ff9800, #e65100); }
.firework-gold { background: radial-gradient(circle, #ffd700, #ff8f00); }
.firework-white { background: radial-gradient(circle, #ffffff, #e0e0e0); }

/* SPECIAL FIREWORK EFFECTS */
.heart-firework {
    position: absolute;
    font-size: 20px;
    color: var(--primary);
    pointer-events: none;
    animation: heartBurst 2s ease-out forwards;
}

.star-firework {
    position: absolute;
    font-size: 16px;
    color: var(--gold);
    pointer-events: none;
    animation: particleSpread 1.5s ease-out forwards;
}

.text-firework {
    position: absolute;
    font-size: 14px;
    font-weight: bold;
    color: var(--text);
    text-shadow: 0 0 10px currentColor;
    pointer-events: none;
    animation: particleSpread 2s ease-out forwards;
}

/* GRAND FINALE EFFECTS */
.finale-burst {
    position: absolute;
    width: 500px;
    height: 500px;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(255, 23, 68, 0.8) 0%,
        rgba(255, 107, 157, 0.6) 30%,
        rgba(255, 215, 0, 0.4) 60%,
        transparent 100%);
    transform: translate(-50%, -50%) scale(0);
    animation: finaleBurst 3s ease-out forwards;
    pointer-events: none;
}

@keyframes finaleBurst {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.8;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* CONTINUOUS SPARKLE EFFECT */
.continuous-sparkle {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, var(--gold), transparent),
        radial-gradient(2px 2px at 40px 70px, var(--primary), transparent),
        radial-gradient(1px 1px at 90px 40px, var(--accent), transparent),
        radial-gradient(1px 1px at 130px 80px, var(--text), transparent);
    background-repeat: repeat;
    background-size: 200px 150px;
    animation: sparkleMove 15s linear infinite;
    opacity: 0.6;
    pointer-events: none;
}

@keyframes sparkleMove {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-150px) rotate(360deg); }
}

/* FIREWORK SOUND EFFECTS SIMULATION */
.sound-wave {
    position: absolute;
    width: 100px;
    height: 100px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: soundWave 1s ease-out forwards;
    pointer-events: none;
}

@keyframes soundWave {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(3);
        opacity: 0;
    }
}

/* FIREWORK LAUNCHER GLOW */
.launcher-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    background: linear-gradient(135deg,
        rgba(255, 23, 68, 0.3) 0%,
        rgba(255, 107, 157, 0.2) 50%,
        rgba(255, 215, 0, 0.3) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.fireworks-launcher:hover .launcher-glow {
    opacity: 1;
    animation: glowPulse 2s ease-in-out infinite;
}

@keyframes glowPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Floating hearts animation */
.hearts {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.heart {
    position: absolute;
    color: var(--primary);
    font-size: 20px;
    animation: floatUp 4s linear infinite;
    opacity: 0;
}

@keyframes floatUp {
    0% {
        opacity: 0;
        transform: translateY(100vh) rotate(0deg) scale(0);
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) rotate(36deg) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(10vh) rotate(324deg) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(0vh) rotate(360deg) scale(0);
    }
}

/* Message card */
#message {
    display: none;
    margin-top: 40px;
    animation: fadeIn 1.2s ease forwards;
    max-width: 400px;
}

.card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 30px 35px;
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

h1 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg,
        var(--primary) 0%,
        var(--accent) 50%,
        var(--primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 15px;
    text-align: center;
    text-shadow: 0 0 30px rgba(255, 23, 68, 0.5);
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: brightness(1); }
    100% { filter: brightness(1.2); }
}

p {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    font-weight: 300;
    line-height: 1.6;
    text-align: center;
    margin: 0;
    color: var(--text-soft);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .flower {
        width: 180px;
        height: 240px;
    }

    .stem {
        height: 140px;
        width: 10px;
    }

    .petals {
        bottom: 120px;
    }

    .center {
        bottom: 145px;
        width: 28px;
        height: 28px;
    }

    .card {
        margin: 20px;
        padding: 25px;
    }

    h1 {
        font-size: 2rem;
    }

    p {
        font-size: 1rem;
    }

    .click-me {
        font-size: 1rem;
    }
}
</style>
</head>

<body>

<!-- Background slideshow -->
<div class="background-slideshow" id="backgroundSlideshow">
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&h=1080&fit=crop')"></div>
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1563241527-3004b7be0ffd?w=1920&h=1080&fit=crop')"></div>
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1582794543139-8ac9cb0f7b11?w=1920&h=1080&fit=crop')"></div>
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&h=1080&fit=crop')"></div>
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=1920&h=1080&fit=crop')"></div>
</div>

<!-- Background overlay -->
<div class="background-overlay"></div>

<div class="hearts" id="heartsContainer"></div>

<div class="scene">
    <div class="click-me" id="hint">🎆 Click to Launch Valentine's Fireworks! 🎆</div>

    <button class="fireworks-launcher" id="fireworksBtn">
        <div class="launcher-glow"></div>
        <div class="launcher-text">🚀 LAUNCH 🚀</div>
    </button>
</div>

<!-- Fireworks container -->
<div class="fireworks-container" id="fireworksContainer"></div>

<!-- Continuous sparkle background -->
<div class="continuous-sparkle" id="continuousSparkle"></div>

    <div id="message" class="card">
        <h1>Happy Valentine’s Day ❤️</h1>
        <p>You light up my world like the most spectacular fireworks display! Every moment with you is an explosion of joy, love, and wonder that fills my heart with brilliant colors. 🎆💕</p>
    </div>
</div>

<script>
const fireworksBtn = document.getElementById("fireworksBtn");
const message = document.getElementById("message");
const hint = document.getElementById("hint");
const heartsContainer = document.getElementById("heartsContainer");
const fireworksContainer = document.getElementById("fireworksContainer");
const backgroundSlideshow = document.getElementById("backgroundSlideshow");

let fireworksLaunched = false;
let currentSlide = 0;
let fireworkCount = 0;

// Background slideshow functionality
function initBackgroundSlideshow() {
    const slides = backgroundSlideshow.querySelectorAll('.background-slide');

    // Show first slide
    slides[0].classList.add('active');

    // Rotate slides every 4 seconds
    setInterval(() => {
        slides[currentSlide].classList.remove('active');
        currentSlide = (currentSlide + 1) % slides.length;
        slides[currentSlide].classList.add('active');
    }, 4000);
}

// Initialize slideshow
initBackgroundSlideshow();

// Create firework explosion
function createFirework(x, y, color = null) {
    const colors = ['firework-red', 'firework-pink', 'firework-purple', 'firework-blue',
                   'firework-cyan', 'firework-green', 'firework-yellow', 'firework-orange',
                   'firework-gold', 'firework-white'];

    const selectedColor = color || colors[Math.floor(Math.random() * colors.length)];

    // Create explosion center
    const explosion = document.createElement('div');
    explosion.className = 'explosion';
    explosion.style.left = x + 'px';
    explosion.style.top = y + 'px';
    fireworksContainer.appendChild(explosion);

    // Create particles
    const particleCount = 20 + Math.random() * 20;
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = `explosion-particle ${selectedColor}`;

        const angle = (i / particleCount) * 360;
        const distance = 50 + Math.random() * 100;
        const dx = Math.cos(angle * Math.PI / 180) * distance;
        const dy = Math.sin(angle * Math.PI / 180) * distance;

        particle.style.setProperty('--dx', dx + 'px');
        particle.style.setProperty('--dy', dy + 'px');
        particle.style.animationDelay = Math.random() * 0.2 + 's';
        particle.style.animationDuration = (1 + Math.random()) + 's';

        explosion.appendChild(particle);
    }

    // Create sound wave effect
    const soundWave = document.createElement('div');
    soundWave.className = 'sound-wave';
    soundWave.style.left = x + 'px';
    soundWave.style.top = y + 'px';
    fireworksContainer.appendChild(soundWave);

    // Remove explosion after animation
    setTimeout(() => {
        if (explosion.parentNode) explosion.parentNode.removeChild(explosion);
        if (soundWave.parentNode) soundWave.parentNode.removeChild(soundWave);
    }, 3000);
}

// Launch rocket with trail
function launchRocket(startX, startY, targetX, targetY, color) {
    const rocket = document.createElement('div');
    rocket.className = 'rocket';
    rocket.style.left = startX + 'px';
    rocket.style.top = startY + 'px';
    fireworksContainer.appendChild(rocket);

    // Animate rocket to target
    rocket.animate([
        { transform: `translate(0, 0) rotate(${Math.atan2(targetY - startY, targetX - startX) * 180 / Math.PI + 90}deg)` },
        { transform: `translate(${targetX - startX}px, ${targetY - startY}px) rotate(${Math.atan2(targetY - startY, targetX - startX) * 180 / Math.PI + 90}deg)` }
    ], {
        duration: 1000,
        easing: 'ease-out'
    });

    // Create explosion at target
    setTimeout(() => {
        createFirework(targetX, targetY, color);
        if (rocket.parentNode) rocket.parentNode.removeChild(rocket);
    }, 1000);
}

// Create heart firework
function createHeartFirework(x, y) {
    const hearts = ['💖', '💕', '💗', '💝', '❤️', '💘'];
    for (let i = 0; i < 8; i++) {
        const heart = document.createElement('div');
        heart.className = 'heart-firework';
        heart.innerHTML = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.left = x + 'px';
        heart.style.top = y + 'px';

        const angle = (i / 8) * 360;
        const distance = 80 + Math.random() * 40;
        const dx = Math.cos(angle * Math.PI / 180) * distance;
        const dy = Math.sin(angle * Math.PI / 180) * distance;

        heart.style.setProperty('--dx', dx + 'px');
        heart.style.setProperty('--dy', dy + 'px');
        heart.style.animationDelay = Math.random() * 0.3 + 's';

        fireworksContainer.appendChild(heart);

        setTimeout(() => {
            if (heart.parentNode) heart.parentNode.removeChild(heart);
        }, 2500);
    }
}

// Main fireworks launch function
fireworksBtn.addEventListener("click", () => {
    if (fireworksLaunched) return;
    fireworksLaunched = true;

    hint.style.display = "none";
    fireworksBtn.style.transform = "scale(0.9)";

    // Launch sequence
    launchFireworksSequence();

    // Show message after initial fireworks
    setTimeout(() => {
        message.style.display = "block";
    }, 3000);

    // Continue with random fireworks
    setTimeout(() => {
        startRandomFireworks();
    }, 5000);
});

// Launch fireworks sequence
function launchFireworksSequence() {
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    const launcherX = centerX;
    const launcherY = window.innerHeight - 100;

    // Launch multiple rockets in sequence
    const launches = [
        { delay: 0, x: centerX - 200, y: centerY - 100, color: 'firework-red' },
        { delay: 500, x: centerX + 200, y: centerY - 100, color: 'firework-pink' },
        { delay: 1000, x: centerX, y: centerY - 200, color: 'firework-gold' },
        { delay: 1500, x: centerX - 100, y: centerY, color: 'firework-purple' },
        { delay: 2000, x: centerX + 100, y: centerY, color: 'firework-blue' },
        { delay: 2500, x: centerX, y: centerY + 50, color: 'firework-white' }
    ];

    launches.forEach(launch => {
        setTimeout(() => {
            launchRocket(launcherX, launcherY, launch.x, launch.y, launch.color);
        }, launch.delay);
    });

    // Heart fireworks finale
    setTimeout(() => {
        createHeartFirework(centerX, centerY - 50);
        createGrandFinale();
    }, 4000);
}

// Grand finale effect
function createGrandFinale() {
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;

    const finale = document.createElement('div');
    finale.className = 'finale-burst';
    finale.style.left = centerX + 'px';
    finale.style.top = centerY + 'px';
    fireworksContainer.appendChild(finale);

    // Multiple simultaneous explosions
    for (let i = 0; i < 5; i++) {
        setTimeout(() => {
            const x = centerX + (Math.random() - 0.5) * 400;
            const y = centerY + (Math.random() - 0.5) * 300;
            createFirework(x, y);
        }, i * 200);
    }

    setTimeout(() => {
        if (finale.parentNode) finale.parentNode.removeChild(finale);
    }, 3000);
}

// Start random fireworks after main sequence
function startRandomFireworks() {
    const randomFireworkInterval = setInterval(() => {
        if (Math.random() < 0.7) { // 70% chance
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * (window.innerHeight * 0.6) + 100;

            if (Math.random() < 0.3) { // 30% chance for heart firework
                createHeartFirework(x, y);
            } else {
                createFirework(x, y);
            }
        }
    }, 2000);

    // Stop random fireworks after 30 seconds
    setTimeout(() => {
        clearInterval(randomFireworkInterval);
    }, 30000);
}

// Create text fireworks
function createTextFirework(x, y, text) {
    const textElement = document.createElement('div');
    textElement.className = 'text-firework';
    textElement.innerHTML = text;
    textElement.style.left = x + 'px';
    textElement.style.top = y + 'px';

    const dx = (Math.random() - 0.5) * 200;
    const dy = (Math.random() - 0.5) * 200;

    textElement.style.setProperty('--dx', dx + 'px');
    textElement.style.setProperty('--dy', dy + 'px');

    fireworksContainer.appendChild(textElement);

    setTimeout(() => {
        if (textElement.parentNode) {
            textElement.parentNode.removeChild(textElement);
        }
    }, 2500);
}

// Enhanced sparkle effect for fireworks
function createFireworkSparkles(x, y) {
    const sparkleTypes = ['✨', '💫', '⭐', '🌟'];
    for (let i = 0; i < 15; i++) {
        setTimeout(() => {
            const sparkle = document.createElement('div');
            sparkle.innerHTML = sparkleTypes[Math.floor(Math.random() * sparkleTypes.length)];
            sparkle.style.position = 'absolute';
            sparkle.style.left = (x + (Math.random() - 0.5) * 200) + 'px';
            sparkle.style.top = (y + (Math.random() - 0.5) * 200) + 'px';
            sparkle.style.fontSize = (Math.random() * 20 + 10) + 'px';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.zIndex = '1000';
            sparkle.style.animation = 'sparkleAnim 2s ease-out forwards';
            sparkle.style.filter = 'drop-shadow(0 0 10px rgba(255, 255, 255, 0.8))';

            document.body.appendChild(sparkle);

            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, 2000);
        }, i * 50);
    }
}

// Add enhanced animations for fireworks
const fireworkStyle = document.createElement('style');
fireworkStyle.textContent = `
    @keyframes sparkleAnim {
        0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
        }
        20% {
            opacity: 1;
            transform: scale(1.2) rotate(72deg);
        }
        80% {
            opacity: 1;
            transform: scale(1) rotate(288deg);
        }
        100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
        }
    }
`;
document.head.appendChild(fireworkStyle);

// Add ambient effects before fireworks
setInterval(() => {
    if (!fireworksLaunched && Math.random() < 0.05) { // 5% chance
        const x = Math.random() * window.innerWidth;
        const y = Math.random() * window.innerHeight * 0.3 + 100;
        createFireworkSparkles(x, y);
    }
}, 3000);

// Add floating hearts occasionally
function createFloatingHeart() {
    const heart = document.createElement('div');
    heart.className = 'heart';
    heart.innerHTML = ['💖', '💕', '💗', '💝', '❤️', '💘'][Math.floor(Math.random() * 6)];
    heart.style.left = Math.random() * 100 + '%';
    heart.style.animationDelay = Math.random() * 2 + 's';
    heart.style.animationDuration = (Math.random() * 2 + 3) + 's';
    heartsContainer.appendChild(heart);

    setTimeout(() => {
        if (heart.parentNode) {
            heart.parentNode.removeChild(heart);
        }
    }, 5000);
}

// Occasional floating hearts
setInterval(() => {
    if (Math.random() < 0.1) { // 10% chance
        createFloatingHeart();
    }
}, 2000);

// Click anywhere to create small firework
document.addEventListener('click', (e) => {
    if (fireworksLaunched && e.target !== fireworksBtn) {
        createFirework(e.clientX, e.clientY);
        createFireworkSparkles(e.clientX, e.clientY);
    }
});
</script>

</body>
</html>
