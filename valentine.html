<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>💖 3D Blooming Valentine's Flower 💖</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600&family=Playfair+Display:wght@400;700;900&family=Dancing+Script:wght@400;700&display=swap" rel="stylesheet">

<style>
:root {
    --bg-1: #0a0a0b;
    --bg-2: #1a1a2e;
    --bg-3: #16213e;
    --primary: #ff1744;
    --primary-light: #ff5983;
    --primary-dark: #c51162;
    --secondary: #e91e63;
    --accent: #ff6b9d;
    --green: #2e7d32;
    --green-light: #4caf50;
    --text: #ffffff;
    --text-soft: #e8eaf6;
    --gold: #ffd700;
    --pink: #ff69b4;
}

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text);
    font-family: 'Inter', sans-serif;
    overflow: hidden;
    position: relative;
}

/* Background slideshow */
.background-slideshow {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.background-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 2s ease-in-out;
}

.background-slide.active {
    opacity: 0.3;
}

/* Overlay to ensure content visibility */
.background-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 23, 68, 0.25) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 157, 0.25) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(26, 26, 46, 0.85) 0%, transparent 50%),
        linear-gradient(135deg, var(--bg-1) 0%, var(--bg-2) 50%, var(--bg-3) 100%);
    z-index: -1;
}

/* Animated background particles */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 107, 157, 0.4), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 23, 68, 0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.2), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: sparkle 20s linear infinite;
    pointer-events: none;
}

@keyframes sparkle {
    0% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
}

.scene {
    perspective: 1200px;
    text-align: center;
    position: relative;
    z-index: 10;
}

.click-me {
    margin-bottom: 20px;
    opacity: 0.8;
    animation: pulse 2s infinite, float 3s ease-in-out infinite;
    font-size: 1.1rem;
    font-weight: 300;
    letter-spacing: 0.5px;
    text-shadow: 0 0 20px rgba(255, 107, 157, 0.5);
    background: linear-gradient(45deg, var(--primary-light), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 3D FLOWER */
.flower {
    position: relative;
    width: 260px;
    height: 320px;
    transform-style: preserve-3d;
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 15px 35px rgba(0, 0, 0, 0.4));
}

.flower:hover {
    transform: scale(1.08) rotateY(5deg);
    filter: drop-shadow(0 20px 40px rgba(255, 23, 68, 0.4));
}

.flower:active {
    transform: scale(0.95);
}

/* Multiple flower layers for depth */
.flower-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
}

.stem {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 170px;
    background: linear-gradient(180deg,
        var(--green-light) 0%,
        var(--green) 50%,
        #1b5e20 100%);
    border-radius: 12px;
    box-shadow:
        inset 2px 0 4px rgba(255, 255, 255, 0.2),
        inset -2px 0 4px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(46, 125, 50, 0.4);
}

.leaf {
    position: absolute;
    width: 55px;
    height: 28px;
    background: linear-gradient(135deg,
        var(--green-light) 0%,
        var(--green) 70%,
        #1b5e20 100%);
    border-radius: 35px 0 35px 0;
    top: 85px;
    left: 8px;
    transform: rotateY(25deg) rotateZ(-25deg);
    box-shadow:
        inset 1px 1px 3px rgba(255, 255, 255, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.2);
}

.leaf::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 10%;
    width: 70%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transform: translateY(-50%) rotate(-20deg);
}

/* Petals container */
.petals {
    position: absolute;
    bottom: 160px;
    left: 50%;
    transform-style: preserve-3d;
    transform: translateX(-50%);
}

/* Outer petals layer */
.petals-outer {
    position: absolute;
    bottom: 160px;
    left: 50%;
    transform-style: preserve-3d;
    transform: translateX(-50%) scale(1.2);
}

/* Inner petals layer */
.petals-inner {
    position: absolute;
    bottom: 165px;
    left: 50%;
    transform-style: preserve-3d;
    transform: translateX(-50%) scale(0.8);
}

/* Single petal */
.petal {
    position: absolute;
    width: 60px;
    height: 105px;
    background: linear-gradient(135deg,
        var(--primary-light) 0%,
        var(--primary) 30%,
        var(--primary-dark) 70%,
        #8e0038 100%);
    border-radius: 50% 50% 50% 50% / 85% 85% 15% 15%;
    transform-origin: bottom center;
    box-shadow:
        inset 0 -20px 30px rgba(0, 0, 0, 0.4),
        inset 0 8px 20px rgba(255, 255, 255, 0.3),
        inset -5px 0 15px rgba(0, 0, 0, 0.2),
        0 15px 30px rgba(0, 0, 0, 0.5),
        0 0 40px rgba(255, 23, 68, 0.4);
    transition: transform 1.2s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

/* Petal highlight */
.petal::before {
    content: '';
    position: absolute;
    top: 8%;
    left: 15%;
    width: 70%;
    height: 50%;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.6) 0%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(255, 255, 255, 0.1) 70%,
        transparent 100%);
    border-radius: 50% 50% 50% 50% / 85% 85% 15% 15%;
    transform: rotate(-8deg);
}

/* Petal vein effect */
.petal::after {
    content: '';
    position: absolute;
    top: 20%;
    left: 50%;
    width: 2px;
    height: 60%;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 100%);
    transform: translateX(-50%);
    border-radius: 2px;
}

/* Outer petals styling */
.petals-outer .petal {
    background: linear-gradient(135deg,
        rgba(255, 89, 131, 0.8) 0%,
        rgba(255, 23, 68, 0.9) 40%,
        rgba(197, 17, 98, 0.8) 100%);
    width: 45px;
    height: 80px;
}

/* Inner petals styling */
.petals-inner .petal {
    background: linear-gradient(135deg,
        var(--accent) 0%,
        var(--secondary) 50%,
        var(--primary) 100%);
    width: 35px;
    height: 65px;
}

/* Initial closed bud */
.flower .petal {
    transform:
        rotateY(var(--y))
        rotateX(75deg)
        translateZ(20px)
        scale(0.5);
}

.flower .petals-outer .petal {
    transform:
        rotateY(var(--y))
        rotateX(80deg)
        translateZ(15px)
        scale(0.4);
}

.flower .petals-inner .petal {
    transform:
        rotateY(var(--y))
        rotateX(85deg)
        translateZ(25px)
        scale(0.3);
}

/* Bloomed 3D state */
.flower.bloomed .petal {
    transform:
        rotateY(var(--y))
        rotateX(-8deg)
        translateZ(80px)
        scale(1.2);
    animation: petalGlow 4s ease-in-out infinite alternate;
}

.flower.bloomed .petals-outer .petal {
    transform:
        rotateY(var(--y))
        rotateX(-15deg)
        translateZ(100px)
        scale(1.4);
    animation: petalGlow 4s ease-in-out infinite alternate 0.5s;
}

.flower.bloomed .petals-inner .petal {
    transform:
        rotateY(var(--y))
        rotateX(5deg)
        translateZ(60px)
        scale(1);
    animation: petalGlow 4s ease-in-out infinite alternate 1s;
}

@keyframes petalGlow {
    0% {
        box-shadow:
            inset 0 -20px 30px rgba(0, 0, 0, 0.4),
            inset 0 8px 20px rgba(255, 255, 255, 0.3),
            inset -5px 0 15px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.5),
            0 0 40px rgba(255, 23, 68, 0.4);
    }
    100% {
        box-shadow:
            inset 0 -20px 30px rgba(0, 0, 0, 0.4),
            inset 0 8px 20px rgba(255, 255, 255, 0.5),
            inset -5px 0 15px rgba(0, 0, 0, 0.2),
            0 15px 30px rgba(0, 0, 0, 0.5),
            0 0 80px rgba(255, 23, 68, 0.8);
    }
}

/* Petal wave animation */
.flower.bloomed .petal {
    animation: petalGlow 4s ease-in-out infinite alternate, petalWave 6s ease-in-out infinite;
}

@keyframes petalWave {
    0%, 100% { transform: rotateY(var(--y)) rotateX(-8deg) translateZ(80px) scale(1.2) rotateZ(0deg); }
    25% { transform: rotateY(var(--y)) rotateX(-8deg) translateZ(80px) scale(1.2) rotateZ(2deg); }
    75% { transform: rotateY(var(--y)) rotateX(-8deg) translateZ(80px) scale(1.2) rotateZ(-2deg); }
}

/* Flower center */
.center {
    position: absolute;
    bottom: 185px;
    left: 50%;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%,
        #fff9c4 0%,
        var(--gold) 30%,
        #ffb300 70%,
        #e65100 100%);
    transform: translateX(-50%) translateZ(30px) scale(0.4);
    box-shadow:
        0 0 20px rgba(255, 215, 0, 0.9),
        inset 0 3px 6px rgba(255, 255, 255, 0.5),
        inset 0 -3px 6px rgba(0, 0, 0, 0.3);
    transition: all 1.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

/* Center highlight */
.center::before {
    content: '';
    position: absolute;
    top: 15%;
    left: 15%;
    width: 70%;
    height: 70%;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0.1) 80%,
        transparent 100%);
}

/* Center texture dots */
.center::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background:
        radial-gradient(circle at 25% 25%, rgba(255, 179, 0, 0.8) 2px, transparent 2px),
        radial-gradient(circle at 75% 25%, rgba(255, 179, 0, 0.6) 1px, transparent 1px),
        radial-gradient(circle at 25% 75%, rgba(255, 179, 0, 0.7) 1.5px, transparent 1.5px),
        radial-gradient(circle at 75% 75%, rgba(255, 179, 0, 0.5) 1px, transparent 1px),
        radial-gradient(circle at 50% 50%, rgba(255, 179, 0, 0.9) 1px, transparent 1px);
    background-size: 8px 8px, 6px 6px, 7px 7px, 5px 5px, 4px 4px;
    transform: translate(-50%, -50%);
}

.flower.bloomed .center {
    transform: translateX(-50%) translateZ(110px) scale(1.6);
    box-shadow:
        0 0 60px rgba(255, 215, 0, 1),
        0 0 120px rgba(255, 215, 0, 0.6),
        0 0 180px rgba(255, 215, 0, 0.3),
        inset 0 3px 8px rgba(255, 255, 255, 0.7),
        inset 0 -3px 8px rgba(0, 0, 0, 0.4);
    animation: centerPulse 3s ease-in-out infinite alternate, centerRotate 8s linear infinite;
}

@keyframes centerPulse {
    0% {
        transform: translateX(-50%) translateZ(110px) scale(1.6);
        filter: brightness(1);
    }
    100% {
        transform: translateX(-50%) translateZ(110px) scale(1.8);
        filter: brightness(1.3);
    }
}

@keyframes centerRotate {
    0% { transform: translateX(-50%) translateZ(110px) scale(1.6) rotate(0deg); }
    100% { transform: translateX(-50%) translateZ(110px) scale(1.6) rotate(360deg); }
}

/* Floating hearts animation */
.hearts {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.heart {
    position: absolute;
    color: var(--primary);
    font-size: 20px;
    animation: floatUp 4s linear infinite;
    opacity: 0;
}

@keyframes floatUp {
    0% {
        opacity: 0;
        transform: translateY(100vh) rotate(0deg) scale(0);
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) rotate(36deg) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(10vh) rotate(324deg) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(0vh) rotate(360deg) scale(0);
    }
}

/* Message card */
#message {
    display: none;
    margin-top: 40px;
    animation: fadeIn 1.2s ease forwards;
    max-width: 400px;
}

.card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 30px 35px;
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

h1 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg,
        var(--primary) 0%,
        var(--accent) 50%,
        var(--primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 15px;
    text-align: center;
    text-shadow: 0 0 30px rgba(255, 23, 68, 0.5);
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: brightness(1); }
    100% { filter: brightness(1.2); }
}

p {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    font-weight: 300;
    line-height: 1.6;
    text-align: center;
    margin: 0;
    color: var(--text-soft);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .flower {
        width: 180px;
        height: 240px;
    }

    .stem {
        height: 140px;
        width: 10px;
    }

    .petals {
        bottom: 120px;
    }

    .center {
        bottom: 145px;
        width: 28px;
        height: 28px;
    }

    .card {
        margin: 20px;
        padding: 25px;
    }

    h1 {
        font-size: 2rem;
    }

    p {
        font-size: 1rem;
    }

    .click-me {
        font-size: 1rem;
    }
}
</style>
</head>

<body>

<!-- Background slideshow -->
<div class="background-slideshow" id="backgroundSlideshow">
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&h=1080&fit=crop')"></div>
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1563241527-3004b7be0ffd?w=1920&h=1080&fit=crop')"></div>
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1582794543139-8ac9cb0f7b11?w=1920&h=1080&fit=crop')"></div>
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=1920&h=1080&fit=crop')"></div>
    <div class="background-slide" style="background-image: url('https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=1920&h=1080&fit=crop')"></div>
</div>

<!-- Background overlay -->
<div class="background-overlay"></div>

<div class="hearts" id="heartsContainer"></div>

<div class="scene">
    <div class="click-me" id="hint">✨ Click the flower to bloom in 3D ✨</div>

    <button class="flower" id="flowerBtn">
        <!-- Outer petals layer -->
        <div class="petals-outer">
            <div class="petal" style="--y:0deg"></div>
            <div class="petal" style="--y:45deg"></div>
            <div class="petal" style="--y:90deg"></div>
            <div class="petal" style="--y:135deg"></div>
            <div class="petal" style="--y:180deg"></div>
            <div class="petal" style="--y:225deg"></div>
            <div class="petal" style="--y:270deg"></div>
            <div class="petal" style="--y:315deg"></div>
        </div>

        <!-- Main petals layer -->
        <div class="petals">
            <div class="petal" style="--y:0deg"></div>
            <div class="petal" style="--y:30deg"></div>
            <div class="petal" style="--y:60deg"></div>
            <div class="petal" style="--y:90deg"></div>
            <div class="petal" style="--y:120deg"></div>
            <div class="petal" style="--y:150deg"></div>
            <div class="petal" style="--y:180deg"></div>
            <div class="petal" style="--y:210deg"></div>
            <div class="petal" style="--y:240deg"></div>
            <div class="petal" style="--y:270deg"></div>
            <div class="petal" style="--y:300deg"></div>
            <div class="petal" style="--y:330deg"></div>
        </div>

        <!-- Inner petals layer -->
        <div class="petals-inner">
            <div class="petal" style="--y:15deg"></div>
            <div class="petal" style="--y:75deg"></div>
            <div class="petal" style="--y:135deg"></div>
            <div class="petal" style="--y:195deg"></div>
            <div class="petal" style="--y:255deg"></div>
            <div class="petal" style="--y:315deg"></div>
        </div>

        <div class="center"></div>

        <div class="stem">
            <div class="leaf"></div>
        </div>
    </button>

    <div id="message" class="card">
        <h1>Happy Valentine’s Day ❤️</h1>
        <p>You make my world bloom in ways I never imagined. Every moment with you is like watching a beautiful flower unfold, revealing new layers of love and wonder. 💕</p>
    </div>
</div>

<script>
const flower = document.getElementById("flowerBtn");
const message = document.getElementById("message");
const hint = document.getElementById("hint");
const heartsContainer = document.getElementById("heartsContainer");
const backgroundSlideshow = document.getElementById("backgroundSlideshow");

let bloomed = false;
let currentSlide = 0;

// Background slideshow functionality
function initBackgroundSlideshow() {
    const slides = backgroundSlideshow.querySelectorAll('.background-slide');

    // Show first slide
    slides[0].classList.add('active');

    // Rotate slides every 4 seconds
    setInterval(() => {
        slides[currentSlide].classList.remove('active');
        currentSlide = (currentSlide + 1) % slides.length;
        slides[currentSlide].classList.add('active');
    }, 4000);
}

// Initialize slideshow
initBackgroundSlideshow();

// Create floating hearts
function createHeart() {
    const heart = document.createElement('div');
    heart.className = 'heart';
    heart.innerHTML = ['💖', '💕', '💗', '💝', '❤️', '💘'][Math.floor(Math.random() * 6)];
    heart.style.left = Math.random() * 100 + '%';
    heart.style.animationDelay = Math.random() * 2 + 's';
    heart.style.animationDuration = (Math.random() * 2 + 3) + 's';
    heartsContainer.appendChild(heart);

    // Remove heart after animation
    setTimeout(() => {
        if (heart.parentNode) {
            heart.parentNode.removeChild(heart);
        }
    }, 5000);
}

// Enhanced hover effects
flower.addEventListener('mouseenter', () => {
    if (!bloomed) {
        flower.style.transform = 'scale(1.08) rotateY(8deg) rotateX(5deg)';
        flower.style.filter = 'drop-shadow(0 20px 40px rgba(255, 23, 68, 0.4)) brightness(1.1)';

        // Add subtle petal movement on hover
        const petals = flower.querySelectorAll('.petal');
        petals.forEach((petal, index) => {
            petal.style.transform = petal.style.transform + ` translateZ(${index % 2 === 0 ? '5px' : '3px'})`;
        });
    }
});

flower.addEventListener('mouseleave', () => {
    if (!bloomed) {
        flower.style.transform = 'scale(1) rotateY(0deg) rotateX(0deg)';
        flower.style.filter = 'drop-shadow(0 15px 35px rgba(0, 0, 0, 0.4))';

        // Reset petal positions
        const petals = flower.querySelectorAll('.petal');
        petals.forEach((petal) => {
            petal.style.transform = petal.style.transform.replace(/translateZ\([^)]*\)/g, '');
        });
    }
});

// Enhanced bloom function
flower.addEventListener("click", () => {
    if (bloomed) return;
    bloomed = true;

    // Add bloom class with staggered animation
    flower.classList.add("bloomed");
    hint.style.display = "none";

    // Create bloom sequence with multiple effects
    setTimeout(() => {
        createBloomBurst();
    }, 200);

    // Show message with delay
    setTimeout(() => {
        message.style.display = "block";
    }, 1200);

    // Start floating hearts with varying intensity
    let heartIntensity = 200;
    const heartInterval = setInterval(() => {
        createHeart();
        // Gradually reduce heart frequency
        heartIntensity += 50;
        if (heartIntensity > 800) {
            clearInterval(heartInterval);
        }
    }, heartIntensity);

    // Enhanced rotation with multiple axes
    flower.animate([
        { transform: "rotateY(0deg) rotateX(0deg)" },
        { transform: "rotateY(360deg) rotateX(10deg)" }
    ], {
        duration: 25000,
        iterations: Infinity,
        easing: "linear"
    });

    // Add multiple sparkle waves
    setTimeout(() => createSparkles(), 800);
    setTimeout(() => createSparkles(), 1500);
    setTimeout(() => createSparkles(), 2200);

    // Add petal shimmer effect
    setTimeout(() => {
        addPetalShimmer();
    }, 1000);
});

// Enhanced sparkle effect
function createSparkles() {
    const sparkleTypes = ['✨', '💫', '⭐', '🌟', '💖', '💕'];
    for (let i = 0; i < 30; i++) {
        setTimeout(() => {
            const sparkle = document.createElement('div');
            sparkle.innerHTML = sparkleTypes[Math.floor(Math.random() * sparkleTypes.length)];
            sparkle.style.position = 'absolute';
            sparkle.style.left = (Math.random() * window.innerWidth) + 'px';
            sparkle.style.top = (Math.random() * window.innerHeight) + 'px';
            sparkle.style.fontSize = (Math.random() * 25 + 15) + 'px';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.zIndex = '1000';
            sparkle.style.animation = 'sparkleAnim 3s ease-out forwards';
            sparkle.style.filter = 'drop-shadow(0 0 10px rgba(255, 255, 255, 0.8))';

            document.body.appendChild(sparkle);

            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, 3000);
        }, i * 80);
    }
}

// Create bloom burst effect
function createBloomBurst() {
    for (let i = 0; i < 15; i++) {
        const burst = document.createElement('div');
        burst.innerHTML = '🌸';
        burst.style.position = 'absolute';
        burst.style.left = '50%';
        burst.style.top = '50%';
        burst.style.fontSize = '20px';
        burst.style.pointerEvents = 'none';
        burst.style.zIndex = '999';
        burst.style.transform = 'translate(-50%, -50%)';

        const angle = (i / 15) * 360;
        const distance = 100 + Math.random() * 100;

        burst.animate([
            {
                transform: 'translate(-50%, -50%) scale(0) rotate(0deg)',
                opacity: 1
            },
            {
                transform: `translate(-50%, -50%) translate(${Math.cos(angle * Math.PI / 180) * distance}px, ${Math.sin(angle * Math.PI / 180) * distance}px) scale(1.5) rotate(360deg)`,
                opacity: 0
            }
        ], {
            duration: 1500,
            easing: 'ease-out'
        });

        document.body.appendChild(burst);

        setTimeout(() => {
            if (burst.parentNode) {
                burst.parentNode.removeChild(burst);
            }
        }, 1500);
    }
}

// Add petal shimmer effect
function addPetalShimmer() {
    const petals = flower.querySelectorAll('.petal');
    petals.forEach((petal, index) => {
        setTimeout(() => {
            petal.style.animation += ', petalShimmer 2s ease-in-out';
        }, index * 100);
    });
}

// Add enhanced animations
const enhancedStyle = document.createElement('style');
enhancedStyle.textContent = `
    @keyframes sparkleAnim {
        0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
        }
        20% {
            opacity: 1;
            transform: scale(1.2) rotate(72deg);
        }
        80% {
            opacity: 1;
            transform: scale(1) rotate(288deg);
        }
        100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
        }
    }

    @keyframes petalShimmer {
        0%, 100% {
            filter: brightness(1) saturate(1);
        }
        50% {
            filter: brightness(1.4) saturate(1.3);
        }
    }
`;
document.head.appendChild(enhancedStyle);

// Add ambient effects
setInterval(() => {
    if (Math.random() < 0.08) { // 8% chance every second
        createHeart();
    }
}, 1200);

// Add occasional sparkles even before bloom
setInterval(() => {
    if (!bloomed && Math.random() < 0.03) { // 3% chance
        const sparkle = document.createElement('div');
        sparkle.innerHTML = '✨';
        sparkle.style.position = 'absolute';
        sparkle.style.left = (Math.random() * window.innerWidth) + 'px';
        sparkle.style.top = (Math.random() * window.innerHeight) + 'px';
        sparkle.style.fontSize = '12px';
        sparkle.style.pointerEvents = 'none';
        sparkle.style.zIndex = '100';
        sparkle.style.animation = 'sparkleAnim 2s ease-out forwards';
        sparkle.style.opacity = '0.6';

        document.body.appendChild(sparkle);

        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 2000);
    }
}, 2000);
</script>

</body>
</html>
