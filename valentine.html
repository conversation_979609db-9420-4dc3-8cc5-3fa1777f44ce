<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>💖 3D Blooming Valentine's Flower 💖</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600&family=Playfair+Display:wght@400;700;900&family=Dancing+Script:wght@400;700&display=swap" rel="stylesheet">

<style>
:root {
    --bg-1: #0a0a0b;
    --bg-2: #1a1a2e;
    --bg-3: #16213e;
    --primary: #ff1744;
    --primary-light: #ff5983;
    --primary-dark: #c51162;
    --secondary: #e91e63;
    --accent: #ff6b9d;
    --green: #2e7d32;
    --green-light: #4caf50;
    --text: #ffffff;
    --text-soft: #e8eaf6;
    --gold: #ffd700;
    --pink: #ff69b4;
}

* {
    box-sizing: border-box;
}

body {
    margin: 0;
    min-height: 100vh;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 23, 68, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 157, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(26, 26, 46, 0.8) 0%, transparent 50%),
        linear-gradient(135deg, var(--bg-1) 0%, var(--bg-2) 50%, var(--bg-3) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text);
    font-family: 'Inter', sans-serif;
    overflow: hidden;
    position: relative;
}

/* Animated background particles */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 107, 157, 0.4), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 23, 68, 0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.2), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: sparkle 20s linear infinite;
    pointer-events: none;
}

@keyframes sparkle {
    0% { transform: translateY(0px) rotate(0deg); opacity: 1; }
    100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
}

.scene {
    perspective: 1200px;
    text-align: center;
    position: relative;
    z-index: 10;
}

.click-me {
    margin-bottom: 20px;
    opacity: 0.8;
    animation: pulse 2s infinite, float 3s ease-in-out infinite;
    font-size: 1.1rem;
    font-weight: 300;
    letter-spacing: 0.5px;
    text-shadow: 0 0 20px rgba(255, 107, 157, 0.5);
    background: linear-gradient(45deg, var(--primary-light), var(--accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 3D FLOWER */
.flower {
    position: relative;
    width: 220px;
    height: 280px;
    transform-style: preserve-3d;
    border: none;
    background: none;
    cursor: pointer;
    transition: all 0.3s ease;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

.flower:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 15px 30px rgba(255, 23, 68, 0.3));
}

.flower:active {
    transform: scale(0.98);
}

.stem {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 170px;
    background: linear-gradient(180deg,
        var(--green-light) 0%,
        var(--green) 50%,
        #1b5e20 100%);
    border-radius: 12px;
    box-shadow:
        inset 2px 0 4px rgba(255, 255, 255, 0.2),
        inset -2px 0 4px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(46, 125, 50, 0.4);
}

.leaf {
    position: absolute;
    width: 55px;
    height: 28px;
    background: linear-gradient(135deg,
        var(--green-light) 0%,
        var(--green) 70%,
        #1b5e20 100%);
    border-radius: 35px 0 35px 0;
    top: 85px;
    left: 8px;
    transform: rotateY(25deg) rotateZ(-25deg);
    box-shadow:
        inset 1px 1px 3px rgba(255, 255, 255, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.2);
}

.leaf::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 10%;
    width: 70%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transform: translateY(-50%) rotate(-20deg);
}

/* Petals container */
.petals {
    position: absolute;
    bottom: 150px;
    left: 50%;
    transform-style: preserve-3d;
    transform: translateX(-50%);
}

/* Single petal */
.petal {
    position: absolute;
    width: 55px;
    height: 95px;
    background: linear-gradient(135deg,
        var(--primary-light) 0%,
        var(--primary) 40%,
        var(--primary-dark) 100%);
    border-radius: 50% 50% 50% 50% / 80% 80% 20% 20%;
    transform-origin: bottom center;
    box-shadow:
        inset 0 -15px 25px rgba(0, 0, 0, 0.3),
        inset 0 5px 15px rgba(255, 255, 255, 0.2),
        0 10px 20px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(255, 23, 68, 0.3);
    transition: transform 1s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.petal::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 20%;
    width: 60%;
    height: 40%;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%);
    border-radius: 50% 50% 50% 50% / 80% 80% 20% 20%;
    transform: rotate(-10deg);
}

/* Initial closed bud */
.flower .petal {
    transform:
        rotateY(var(--y))
        rotateX(70deg)
        translateZ(15px)
        scale(0.6);
}

/* Bloomed 3D state */
.flower.bloomed .petal {
    transform:
        rotateY(var(--y))
        rotateX(-5deg)
        translateZ(70px)
        scale(1.1);
    animation: petalGlow 3s ease-in-out infinite alternate;
}

@keyframes petalGlow {
    0% {
        box-shadow:
            inset 0 -15px 25px rgba(0, 0, 0, 0.3),
            inset 0 5px 15px rgba(255, 255, 255, 0.2),
            0 10px 20px rgba(0, 0, 0, 0.4),
            0 0 30px rgba(255, 23, 68, 0.3);
    }
    100% {
        box-shadow:
            inset 0 -15px 25px rgba(0, 0, 0, 0.3),
            inset 0 5px 15px rgba(255, 255, 255, 0.3),
            0 10px 20px rgba(0, 0, 0, 0.4),
            0 0 50px rgba(255, 23, 68, 0.6);
    }
}

/* Flower center */
.center {
    position: absolute;
    bottom: 175px;
    left: 50%;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%,
        #fff59d 0%,
        var(--gold) 40%,
        #ff8f00 100%);
    transform: translateX(-50%) translateZ(25px) scale(0.5);
    box-shadow:
        0 0 15px rgba(255, 215, 0, 0.8),
        inset 0 2px 4px rgba(255, 255, 255, 0.4);
    transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.center::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(255, 255, 255, 0.2) 70%,
        transparent 100%);
}

.flower.bloomed .center {
    transform: translateX(-50%) translateZ(90px) scale(1.4);
    box-shadow:
        0 0 40px rgba(255, 215, 0, 1),
        0 0 80px rgba(255, 215, 0, 0.5),
        inset 0 2px 6px rgba(255, 255, 255, 0.6);
    animation: centerPulse 2s ease-in-out infinite alternate;
}

@keyframes centerPulse {
    0% { transform: translateX(-50%) translateZ(90px) scale(1.4); }
    100% { transform: translateX(-50%) translateZ(90px) scale(1.6); }
}

/* Floating hearts animation */
.hearts {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.heart {
    position: absolute;
    color: var(--primary);
    font-size: 20px;
    animation: floatUp 4s linear infinite;
    opacity: 0;
}

@keyframes floatUp {
    0% {
        opacity: 0;
        transform: translateY(100vh) rotate(0deg) scale(0);
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) rotate(36deg) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(10vh) rotate(324deg) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(0vh) rotate(360deg) scale(0);
    }
}

/* Message card */
#message {
    display: none;
    margin-top: 40px;
    animation: fadeIn 1.2s ease forwards;
    max-width: 400px;
}

.card {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 30px 35px;
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

h1 {
    font-family: 'Dancing Script', cursive;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg,
        var(--primary) 0%,
        var(--accent) 50%,
        var(--primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 15px;
    text-align: center;
    text-shadow: 0 0 30px rgba(255, 23, 68, 0.5);
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: brightness(1); }
    100% { filter: brightness(1.2); }
}

p {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    font-weight: 300;
    line-height: 1.6;
    text-align: center;
    margin: 0;
    color: var(--text-soft);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .flower {
        width: 180px;
        height: 240px;
    }

    .stem {
        height: 140px;
        width: 10px;
    }

    .petals {
        bottom: 120px;
    }

    .center {
        bottom: 145px;
        width: 28px;
        height: 28px;
    }

    .card {
        margin: 20px;
        padding: 25px;
    }

    h1 {
        font-size: 2rem;
    }

    p {
        font-size: 1rem;
    }

    .click-me {
        font-size: 1rem;
    }
}
</style>
</head>

<body>

<div class="hearts" id="heartsContainer"></div>

<div class="scene">
    <div class="click-me" id="hint">✨ Click the flower to bloom in 3D ✨</div>

    <button class="flower" id="flowerBtn">
        <div class="petals">
            <!-- 12 petals for fuller bloom -->
            <div class="petal" style="--y:0deg"></div>
            <div class="petal" style="--y:30deg"></div>
            <div class="petal" style="--y:60deg"></div>
            <div class="petal" style="--y:90deg"></div>
            <div class="petal" style="--y:120deg"></div>
            <div class="petal" style="--y:150deg"></div>
            <div class="petal" style="--y:180deg"></div>
            <div class="petal" style="--y:210deg"></div>
            <div class="petal" style="--y:240deg"></div>
            <div class="petal" style="--y:270deg"></div>
            <div class="petal" style="--y:300deg"></div>
            <div class="petal" style="--y:330deg"></div>
        </div>

        <div class="center"></div>

        <div class="stem">
            <div class="leaf"></div>
        </div>
    </button>

    <div id="message" class="card">
        <h1>Happy Valentine’s Day ❤️</h1>
        <p>You make my world bloom in ways I never imagined. Every moment with you is like watching a beautiful flower unfold, revealing new layers of love and wonder. 💕</p>
    </div>
</div>

<script>
const flower = document.getElementById("flowerBtn");
const message = document.getElementById("message");
const hint = document.getElementById("hint");
const heartsContainer = document.getElementById("heartsContainer");

let bloomed = false;

// Create floating hearts
function createHeart() {
    const heart = document.createElement('div');
    heart.className = 'heart';
    heart.innerHTML = ['💖', '💕', '💗', '💝', '❤️', '💘'][Math.floor(Math.random() * 6)];
    heart.style.left = Math.random() * 100 + '%';
    heart.style.animationDelay = Math.random() * 2 + 's';
    heart.style.animationDuration = (Math.random() * 2 + 3) + 's';
    heartsContainer.appendChild(heart);

    // Remove heart after animation
    setTimeout(() => {
        if (heart.parentNode) {
            heart.parentNode.removeChild(heart);
        }
    }, 5000);
}

// Add hover effect
flower.addEventListener('mouseenter', () => {
    if (!bloomed) {
        flower.style.transform = 'scale(1.05) rotateY(10deg)';
    }
});

flower.addEventListener('mouseleave', () => {
    if (!bloomed) {
        flower.style.transform = 'scale(1) rotateY(0deg)';
    }
});

// Main bloom function
flower.addEventListener("click", () => {
    if (bloomed) return;
    bloomed = true;

    // Add bloom class
    flower.classList.add("bloomed");
    hint.style.display = "none";

    // Show message with delay
    setTimeout(() => {
        message.style.display = "block";
    }, 800);

    // Start floating hearts
    const heartInterval = setInterval(createHeart, 300);

    // Stop hearts after 10 seconds
    setTimeout(() => {
        clearInterval(heartInterval);
    }, 10000);

    // Gentle rotation after bloom
    flower.animate([
        { transform: "rotateY(0deg)" },
        { transform: "rotateY(360deg)" }
    ], {
        duration: 20000,
        iterations: Infinity,
        easing: "linear"
    });

    // Add sparkle effect
    setTimeout(() => {
        createSparkles();
    }, 1000);
});

// Create sparkle effect
function createSparkles() {
    for (let i = 0; i < 20; i++) {
        setTimeout(() => {
            const sparkle = document.createElement('div');
            sparkle.innerHTML = '✨';
            sparkle.style.position = 'absolute';
            sparkle.style.left = (Math.random() * window.innerWidth) + 'px';
            sparkle.style.top = (Math.random() * window.innerHeight) + 'px';
            sparkle.style.fontSize = (Math.random() * 20 + 10) + 'px';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.zIndex = '1000';
            sparkle.style.animation = 'sparkleAnim 2s ease-out forwards';

            document.body.appendChild(sparkle);

            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, 2000);
        }, i * 100);
    }
}

// Add sparkle animation
const sparkleStyle = document.createElement('style');
sparkleStyle.textContent = `
    @keyframes sparkleAnim {
        0% {
            opacity: 0;
            transform: scale(0) rotate(0deg);
        }
        50% {
            opacity: 1;
            transform: scale(1) rotate(180deg);
        }
        100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
        }
    }
`;
document.head.appendChild(sparkleStyle);

// Add some initial ambient hearts
setInterval(() => {
    if (Math.random() < 0.1) { // 10% chance every second
        createHeart();
    }
}, 1000);
</script>

</body>
</html>
